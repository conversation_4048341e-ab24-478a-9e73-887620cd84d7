<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复古贪食蛇</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2c1810, #1a0f08);
            font-family: 'Press Start 2P', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #d4af37;
        }

        .game-container {
            text-align: center;
            background: #0f0a06;
            padding: 40px;
            border-radius: 0;
            box-shadow: 
                0 0 0 4px #8b4513,
                0 0 0 8px #654321,
                0 0 20px rgba(212, 175, 55, 0.3);
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #ffd700;
            text-shadow: 2px 2px 0 #8b4513;
            letter-spacing: 2px;
        }

        #gameCanvas {
            border: 4px solid #8b4513;
            background: #1a1a1a;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }

        .score-board {
            margin: 20px 0;
            font-size: 14px;
            color: #daa520;
        }

        .controls {
            margin-top: 20px;
            font-size: 10px;
            color: #b8860b;
            line-height: 1.8;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(15, 10, 6, 0.9);
            padding: 30px;
            border: 2px solid #8b4513;
            display: none;
            z-index: 10;
        }

        .restart-btn {
            background: #8b4513;
            color: #ffd700;
            border: none;
            padding: 10px 20px;
            font-family: 'Press Start 2P', monospace;
            font-size: 10px;
            cursor: pointer;
            margin-top: 15px;
        }

        .restart-btn:hover {
            background: #a0522d;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>复古贪食蛇</h1>
        <div class="score-board">
            <div>得分: <span id="score">0</span></div>
            <div>最高分: <span id="highScore">0</span></div>
        </div>
        <canvas id="gameCanvas" width="400" height="400"></canvas>
        <div class="controls">
            使用方向键控制移动<br>
            按空格键暂停/继续
        </div>
        <div class="game-over" id="gameOver">
            <div>游戏结束!</div>
            <div>最终得分: <span id="finalScore">0</span></div>
            <button class="restart-btn" onclick="restartGame()">重新开始</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const gameOverElement = document.getElementById('gameOver');
        const finalScoreElement = document.getElementById('finalScore');

        // 复古配色方案
        const colors = {
            background: '#1a1a1a',
            snake: '#228b22',
            snakeHead: '#32cd32',
            food: '#dc143c',
            grid: '#2f2f2f'
        };

        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        let snake = [{x: 10, y: 10}];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = true;
        let gamePaused = false;

        highScoreElement.textContent = highScore;

        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // 确保食物不会出现在蛇身上
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    break;
                }
            }
        }

        function drawGame() {
            // 清空画布
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制网格
            ctx.strokeStyle = colors.grid;
            ctx.lineWidth = 0.5;
            for (let i = 0; i <= tileCount; i++) {
                ctx.beginPath();
                ctx.moveTo(i * gridSize, 0);
                ctx.lineTo(i * gridSize, canvas.height);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(0, i * gridSize);
                ctx.lineTo(canvas.width, i * gridSize);
                ctx.stroke();
            }

            // 绘制蛇
            snake.forEach((segment, index) => {
                if (index === 0) {
                    ctx.fillStyle = colors.snakeHead;
                } else {
                    ctx.fillStyle = colors.snake;
                }
                
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
                
                // 添加像素化效果
                ctx.fillStyle = index === 0 ? '#90ee90' : '#90ee90';
                ctx.fillRect(segment.x * gridSize + 2, segment.y * gridSize + 2, 4, 4);
            });

            // 绘制食物
            ctx.fillStyle = colors.food;
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
            
            // 食物的像素化效果
            ctx.fillStyle = '#ff69b4';
            ctx.fillRect(food.x * gridSize + 4, food.y * gridSize + 4, 6, 6);
        }

        function moveSnake() {
            if (!gameRunning || gamePaused) return;

            const head = {x: snake[0].x + dx, y: snake[0].y + dy};

            // 检查碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
            } else {
                snake.pop();
            }
        }

        function gameOver() {
            gameRunning = false;
            if (score > highScore) {
                highScore = score;
                highScoreElement.textContent = highScore;
                localStorage.setItem('snakeHighScore', highScore);
            }
            finalScoreElement.textContent = score;
            gameOverElement.style.display = 'block';
        }

        function restartGame() {
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            gameRunning = true;
            gamePaused = false;
            gameOverElement.style.display = 'none';
            generateFood();
        }

        function gameLoop() {
            moveSnake();
            drawGame();
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning) return;

            switch(e.key) {
                case 'ArrowUp':
                    if (dy === 0) {
                        dx = 0;
                        dy = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (dy === 0) {
                        dx = 0;
                        dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (dx === 0) {
                        dx = -1;
                        dy = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (dx === 0) {
                        dx = 1;
                        dy = 0;
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    gamePaused = !gamePaused;
                    break;
            }
        });

        // 初始化游戏
        generateFood();
        setInterval(gameLoop, 150);
    </script>
</body>
</html>
