#include <iostream>
using namespace std;
int main()
{
    // 1.算数运算符
    int a = 20, b = 6;
    cout << "a+b = " << a + b << endl;
    cout << "a/b= " << a / b << endl;
    // 整数除法得到的结果仍是整数

    short a2 = 3;
    long long b2 = 23435;
    // 超出数据类型表示的上限会?
    cout << "a2 *b2= " << endl;         // 把字节小的自动转成字节大的类型
    cout << "b2/b= " << b2 / b << endl; // 整数除法

    float a3 = 20;
    cout << "a3/b" << a3 / b << endl; // 想要用带小数的数据,一个变量定义为浮点型就可以

    cout << "a%b=" << a % b << endl;
    cout << "-a/b= " << -a / b << endl; // 余数不全为正
    //! 取模/取余 只能针对整型变量

    // 2.赋值运算符
    a = 1;
    a = b = 5;
    // 复合赋值
    int sum, c;
    sum = a;
    // sum = sum + b;
    // sum = sum + c;

    sum += b;
    sum += c;
    cout << "a+ b +c = " << sum << endl;
    // 递增递减运算符
    cout << "++a= " << ++a << endl;

    int i = 0, j;
    j = ++i;
    cout << "j = " << j << endl;
    // 在赋值时用i++,则先把已有值赋给左侧,j还是0,之后i变为1;
    cout << "---------------" << endl;

    // 3.关系和逻辑运算符
    cout << "1<2" << (1 < 2) << endl;
    cout << "a<b" << (a < b) << endl;

    cout << "1<2&&3>5" << (1 < 2 && 3 > 5) << endl;
    cout << "!(1<2||3>=5)" << !(1 < 2 || 3 >= 5) << endl;

    cin.get();
}