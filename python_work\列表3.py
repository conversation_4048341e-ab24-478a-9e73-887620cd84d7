#练习3-8
places= ['Taiwan','Shanghai','Japan','Ice land','Maldives']
print(places)

print(sorted(places))#sorted()函数 临时按照字母顺序排序
print(places)#列表实际顺序未改

print(sorted(places,reverse=True))#传递reverse=True参数 临时按照字母相反顺序排序
print(places)#列表实际顺序未改

places.reverse()#reverse()方法 永久性反转列表元素顺序
print(places)
places.reverse()#再次调用reverse()方法恢复原顺序
print(places)

places.sort()#sort()方法 永久性修改列表元素顺序为字母顺序
print(places)
places.sort(reverse=True)#传递参数 字母倒序排序
print(places)

#练习3-9
jiabin = ['Pantao','Liu jun wei','sunbin']
print(f"邀请了{len(jiabin)}个人")
#练习3-10
#使用各种函数sorted(listname) len(listname) 永久性.sort() .reverse()
#包括.append() .insert(index,value) del .pop(index) .remove(value)

like = ['New York','Tokoyo','Ice Land','Beijing']
#先确认长度
print(f"你喜欢的事物一共有{len(like)}")
#修改 增加 减少
like[3] = 'Shanghai'
print(like)
like.append('Beijing')
print(like)
like.insert(2,'Sanya')
print(like)

del like[3]
like.pop(1)
like.remove('Beijing')
print(like)

#组织
like.sort(reverse=True)
print(like)
print(sorted(like))

print(like)
like.reverse()
print(like)
