#练习4-3
for value in range(1,21):
    print(value)

#练习4-4
baiwan = [a for a in range(1,1000001)]#baiwan = list(range(1,1000001))
# for num in baiwan:
#     print(num)

#练习4-5 依旧用baiwan[]
print(f"{min(baiwan)},{max(baiwan)},{sum(baiwan)}")

#练习4-6
jishubiao = [jishu for jishu in range(1,20,2)]#jishubiao = list(range(1,20,2))
print(jishubiao)
for jishu in jishubiao:
    print(jishu)

#练习4-7
zhengchu3 = list(range(3,31,3))
print(zhengchu3)
for c in zhengchu3:
    print(c)

#练习4-8
lifang = [num**3 for num in range(1,11)]
print(lifang)
for number in lifang:
    print(number)

#练习4-9
#用方法2
lifangzhi = []
for e in range(1,11):
    f = e**3
    lifangzhi.append(f)##两行合并lifangzhi.append(e**3)
print(lifangzhi)
for g in lifangzhi:
    print(g)
