#include <iostream>
using namespace std;
int main()
{
    // 整形
    short a = 20;
    cout << "a=" << a << endl;
    cout << "a的长度:" << sizeof a << endl; // sizeof提取字节数

    int a1 = 20;
    cout << "a1=" << a1 << endl;
    cout << "a1的长度:" << sizeof a1 << endl;

    long a2 = 20;
    cout << "a2=" << a2 << endl;
    cout << "a2的长度:" << sizeof a2 << endl;

    long long a3 = 20;
    cout << "a3=" << a3 << endl;
    cout << "a3的长度:" << sizeof a3 << endl;

    // 无符号整形
    unsigned short s1 = 32767; // short若为32768会--回退--到 -32768;unsigned short给-1会绕回65535(数据溢出),不会报错
    cout << "s1= " << s1 << endl;
    // 字符类型
    char c = 65; // 直接打字母需要'A'
    cout << "c = " << c << endl;
    cout << "c + 1 = " << c + 1 << endl;
    // bool类型
    bool b1 = true;
    cout << "b1 = " << b1 << endl;
    cout << "b1长度:" << sizeof b1 << endl;

    // 浮点类型
    float f = 2.5;
    double d = 3.79E-23;
    cout << "f = " << f << endl;
    cout << "d = " << d << endl;
    // 字面值常量
    // 整形
    30;
    036L;
    0x1ELL;
    // 浮点类型
    3.14f;
    1.25L; // long double
    // 字符类型
    'A';
    ',';
    '2';
    "abc";
    '\''; // 表述单引号时加一个\转移字符
    // 转义字符 重要!
    char tc = '\n';
    cout << "tc = " << tc << endl; // 输出一个换行符

    cout << "Hello world!\t\"Hello C++\"\n \? " << endl;
    

    cin.get();
}