#联系2-3
name = "<PERSON>"
message = f"Hello {name},would you like to learn some Python today?"
print(message)
#联系2-4
name = "Sun bin"
print(name.lower())
print(name.upper())
print(name.title())
#联系2-5 6
famous_person = "Albert Einstein"
message = f'{famous_person} once said, "Aperson who never made a mistake never tried anything new."'
print(message)
#联系2-7
name = "\tSun bin\n"#\n换行已经是空白字符了 \t空格  空白字符包括空格 制表符 换行符  空白字符的数量不重要，python只会识别一个空格字符，并将其中的所有空白字符都删除。
print(name)
print(name.lstrip())#lstrip是把字符串前面空白删除   \t被删了
print(name.rstrip())#rstrip是把字符串后面空白删除 \n被删了
print(name.strip())#strip是把字符串前后空白删除 \t \n都删了
