<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .game-container {
            text-align: center;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .score-board {
            display: flex;
            justify-content: space-between;
            width: 400px;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #555;
        }

        .game-board {
            border: 3px solid #333;
            background-color: #f0f0f0;
            border-radius: 5px;
        }

        .controls {
            margin-top: 20px;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            transition: transform 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .instructions {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }

        .game-over h2 {
            margin-top: 0;
            color: #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇游戏</h1>
        
        <div class="score-board">
            <span>得分: <span id="score">0</span></span>
            <span>最高分: <span id="highScore">0</span></span>
        </div>

        <canvas id="gameBoard" class="game-board" width="400" height="400"></canvas>

        <div class="controls">
            <button onclick="startGame()">开始游戏</button>
            <button onclick="pauseGame()">暂停/继续</button>
            <button onclick="resetGame()">重新开始</button>
        </div>

        <div class="instructions">
            <p>🎮 <strong>移动:</strong> 方向键 或 WASD</p>
            <p>⏸️ <strong>暂停:</strong> 空格键 或 ESC</p>
            <p>🚀 <strong>开始:</strong> 回车键 或 空格键</p>
            <p>🍎 吃食物来增长身体并获得分数</p>
            <p>⚠️ 避免撞到墙壁或自己的身体</p>
            <p id="gameStats" style="margin-top: 10px; font-size: 12px; color: #888;"></p>
        </div>
    </div>

    <div id="gameOver" class="game-over">
        <h2>游戏结束!</h2>
        <p>最终得分: <span id="finalScore">0</span></p>
        <button onclick="resetGame()">再玩一次</button>
    </div>

    <script>
        // 游戏常量
        const GAME_CONFIG = {
            GRID_SIZE: 20,
            INITIAL_SPEED: 150,
            MIN_SPEED: 50,
            SPEED_INCREASE: 10,
            SCORE_PER_FOOD: 10,
            SPEED_INCREASE_INTERVAL: 50
        };

        // DOM 元素
        const canvas = document.getElementById('gameBoard');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const finalScoreElement = document.getElementById('finalScore');
        const gameOverElement = document.getElementById('gameOver');

        // 游戏尺寸
        const gridWidth = canvas.width / GAME_CONFIG.GRID_SIZE;
        const gridHeight = canvas.height / GAME_CONFIG.GRID_SIZE;

        // 游戏状态变量
        let snake = [];
        let food = {};
        let direction = 'right';
        let nextDirection = 'right';
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gamePaused = false;
        let gameLoop;
        let speed = GAME_CONFIG.INITIAL_SPEED;

        // 预绘制网格背景
        let gridCanvas = null;
        let gridCtx = null;

        // 创建网格背景
        function createGridBackground() {
            if (!gridCanvas) {
                gridCanvas = document.createElement('canvas');
                gridCanvas.width = canvas.width;
                gridCanvas.height = canvas.height;
                gridCtx = gridCanvas.getContext('2d');
            }

            // 清空背景
            gridCtx.fillStyle = '#f0f0f0';
            gridCtx.fillRect(0, 0, gridCanvas.width, gridCanvas.height);

            // 绘制网格线
            gridCtx.strokeStyle = '#e0e0e0';
            gridCtx.lineWidth = 0.5;
            for (let i = 0; i <= gridWidth; i++) {
                gridCtx.beginPath();
                gridCtx.moveTo(i * GAME_CONFIG.GRID_SIZE, 0);
                gridCtx.lineTo(i * GAME_CONFIG.GRID_SIZE, gridCanvas.height);
                gridCtx.stroke();
            }
            for (let i = 0; i <= gridHeight; i++) {
                gridCtx.beginPath();
                gridCtx.moveTo(0, i * GAME_CONFIG.GRID_SIZE);
                gridCtx.lineTo(gridCanvas.width, i * GAME_CONFIG.GRID_SIZE);
                gridCtx.stroke();
            }
        }

        // 初始化游戏
        function initGame() {
            // 初始化蛇
            snake = [
                {x: 10, y: 10},
                {x: 9, y: 10},
                {x: 8, y: 10}
            ];

            // 生成食物
            generateFood();

            // 重置方向和状态
            direction = 'right';
            nextDirection = 'right';
            gamePaused = false;
            speed = GAME_CONFIG.INITIAL_SPEED;

            // 重置分数
            score = 0;
            scoreElement.textContent = score;
            highScoreElement.textContent = highScore;

            // 创建网格背景
            createGridBackground();
        }

        // 优化的食物生成算法
        function generateFood() {
            // 收集所有空位置
            const emptyCells = [];
            for (let x = 0; x < gridWidth; x++) {
                for (let y = 0; y < gridHeight; y++) {
                    const isSnake = snake.some(segment => segment.x === x && segment.y === y);
                    if (!isSnake) {
                        emptyCells.push({x, y});
                    }
                }
            }

            // 随机选择一个空位置
            if (emptyCells.length > 0) {
                food = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            } else {
                // 游戏胜利（理论上不会发生，除非蛇填满整个画布）
                gameOver();
            }
        }

        // 优化的绘制函数
        function draw() {
            // 使用预绘制的网格背景
            ctx.drawImage(gridCanvas, 0, 0);

            // 如果游戏暂停，添加半透明遮罩
            if (gamePaused) {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 显示暂停文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('游戏暂停', canvas.width / 2, canvas.height / 2);
                ctx.fillText('按空格键继续', canvas.width / 2, canvas.height / 2 + 30);
                return;
            }

            // 绘制蛇
            snake.forEach((segment, index) => {
                const x = segment.x * GAME_CONFIG.GRID_SIZE;
                const y = segment.y * GAME_CONFIG.GRID_SIZE;
                const size = GAME_CONFIG.GRID_SIZE - 1;

                if (index === 0) {
                    // 蛇头 - 更亮的绿色
                    ctx.fillStyle = '#4CAF50';
                    ctx.fillRect(x, y, size, size);

                    // 蛇头的眼睛
                    ctx.fillStyle = '#000';
                    const eyeSize = 3;
                    // 根据方向绘制眼睛
                    switch (direction) {
                        case 'right':
                            ctx.fillRect(x + size * 0.7, y + size * 0.2, eyeSize, eyeSize);
                            ctx.fillRect(x + size * 0.7, y + size * 0.6, eyeSize, eyeSize);
                            break;
                        case 'left':
                            ctx.fillRect(x + size * 0.2, y + size * 0.2, eyeSize, eyeSize);
                            ctx.fillRect(x + size * 0.2, y + size * 0.6, eyeSize, eyeSize);
                            break;
                        case 'up':
                            ctx.fillRect(x + size * 0.2, y + size * 0.2, eyeSize, eyeSize);
                            ctx.fillRect(x + size * 0.6, y + size * 0.2, eyeSize, eyeSize);
                            break;
                        case 'down':
                            ctx.fillRect(x + size * 0.2, y + size * 0.7, eyeSize, eyeSize);
                            ctx.fillRect(x + size * 0.6, y + size * 0.7, eyeSize, eyeSize);
                            break;
                    }
                } else {
                    // 蛇身 - 渐变效果
                    const alpha = Math.max(0.6, 1 - index * 0.05);
                    ctx.fillStyle = `rgba(139, 195, 74, ${alpha})`;
                    ctx.fillRect(x, y, size, size);
                }
            });

            // 绘制食物 - 添加动画效果
            const time = Date.now() * 0.005;
            const pulse = Math.sin(time) * 0.1 + 0.9;
            const foodSize = (GAME_CONFIG.GRID_SIZE / 2 - 1) * pulse;

            ctx.fillStyle = '#FF5722';
            ctx.beginPath();
            ctx.arc(
                food.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                food.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                foodSize,
                0,
                Math.PI * 2
            );
            ctx.fill();

            // 食物光晕效果
            ctx.fillStyle = 'rgba(255, 87, 34, 0.3)';
            ctx.beginPath();
            ctx.arc(
                food.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                food.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                foodSize + 3,
                0,
                Math.PI * 2
            );
            ctx.fill();
        }

        // 移动蛇
        function moveSnake() {
            direction = nextDirection;
            
            // 创建新头部
            const head = {...snake[0]};
            
            switch (direction) {
                case 'up':
                    head.y -= 1;
                    break;
                case 'down':
                    head.y += 1;
                    break;
                case 'left':
                    head.x -= 1;
                    break;
                case 'right':
                    head.x += 1;
                    break;
            }
            
            // 检查碰撞墙壁
            if (head.x < 0 || head.x >= gridWidth || head.y < 0 || head.y >= gridHeight) {
                gameOver();
                return;
            }
            
            // 检查碰撞自己
            for (let segment of snake) {
                if (segment.x === head.x && segment.y === head.y) {
                    gameOver();
                    return;
                }
            }
            
            // 将新头部添加到蛇的开头
            snake.unshift(head);
            
            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                // 增加分数
                score += GAME_CONFIG.SCORE_PER_FOOD;
                scoreElement.textContent = score;

                // 生成新食物
                generateFood();

                // 增加速度（每50分加速一次）
                if (score % GAME_CONFIG.SPEED_INCREASE_INTERVAL === 0 && speed > GAME_CONFIG.MIN_SPEED) {
                    speed -= GAME_CONFIG.SPEED_INCREASE;
                    // 重新设置定时器以应用新速度
                    if (gameLoop) {
                        clearInterval(gameLoop);
                        gameLoop = setInterval(gameUpdate, speed);
                    }
                }

                // 播放吃食物音效（如果有的话）
                playEatSound();
            } else {
                // 移除尾部
                snake.pop();
            }
        }

        // 游戏主循环
        function gameUpdate() {
            moveSnake();
            draw();
        }

        // 开始游戏
        function startGame() {
            if (gameRunning) return;
            
            initGame();
            gameRunning = true;
            gameOverElement.style.display = 'none';
            
            gameLoop = setInterval(gameUpdate, speed);
        }

        // 暂停/继续游戏
        function pauseGame() {
            if (!gameRunning) return;

            gamePaused = !gamePaused;

            if (gamePaused) {
                clearInterval(gameLoop);
                gameLoop = null;
            } else {
                gameLoop = setInterval(gameUpdate, speed);
            }

            // 重新绘制以显示暂停状态
            draw();
        }

        // 重置游戏
        function resetGame() {
            if (gameLoop) {
                clearInterval(gameLoop);
                gameLoop = null;
            }
            gameRunning = false;
            gameOverElement.style.display = 'none';
            initGame();
            draw();
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            gamePaused = false;
            clearInterval(gameLoop);
            gameLoop = null;

            // 播放游戏结束音效
            playGameOverSound();

            // 更新游戏统计
            updateGameStats();

            // 更新最高分
            if (score > highScore) {
                highScore = score;
                localStorage.setItem('snakeHighScore', highScore);
                highScoreElement.textContent = highScore;
            }

            // 显示游戏结束界面
            finalScoreElement.textContent = score;
            gameOverElement.style.display = 'block';
        }

        // 游戏统计
        function updateGameStats() {
            const stats = JSON.parse(localStorage.getItem('snakeGameStats') || '{}');
            stats.gamesPlayed = (stats.gamesPlayed || 0) + 1;
            stats.totalScore = (stats.totalScore || 0) + score;
            stats.averageScore = Math.round(stats.totalScore / stats.gamesPlayed);

            if (!stats.bestScore || score > stats.bestScore) {
                stats.bestScore = score;
            }

            localStorage.setItem('snakeGameStats', JSON.stringify(stats));
        }

        // 音效函数（简单的音效实现）
        function playEatSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // 静默处理音效错误
            }
        }

        function playGameOverSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(200, audioContext.currentTime + 0.3);

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (e) {
                // 静默处理音效错误
            }
        }

        // 改进的键盘控制
        document.addEventListener('keydown', (e) => {
            // 防止重复按键
            if (e.repeat) return;

            switch (e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    if (gameRunning && !gamePaused && direction !== 'down') {
                        nextDirection = 'up';
                    }
                    e.preventDefault();
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    if (gameRunning && !gamePaused && direction !== 'up') {
                        nextDirection = 'down';
                    }
                    e.preventDefault();
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    if (gameRunning && !gamePaused && direction !== 'right') {
                        nextDirection = 'left';
                    }
                    e.preventDefault();
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    if (gameRunning && !gamePaused && direction !== 'left') {
                        nextDirection = 'right';
                    }
                    e.preventDefault();
                    break;
                case ' ':
                case 'Spacebar':
                    if (gameRunning) {
                        pauseGame();
                    } else {
                        startGame();
                    }
                    e.preventDefault();
                    break;
                case 'Enter':
                    if (!gameRunning) {
                        startGame();
                    }
                    e.preventDefault();
                    break;
                case 'Escape':
                    if (gameRunning) {
                        pauseGame();
                    }
                    e.preventDefault();
                    break;
            }
        });

        // 显示游戏统计
        function displayGameStats() {
            const stats = JSON.parse(localStorage.getItem('snakeGameStats') || '{}');
            const statsElement = document.getElementById('gameStats');

            if (stats.gamesPlayed) {
                statsElement.textContent = `游戏次数: ${stats.gamesPlayed} | 平均分: ${stats.averageScore || 0} | 历史最高: ${stats.bestScore || 0}`;
            } else {
                statsElement.textContent = '开始您的第一局游戏！';
            }
        }

        // 游戏主循环优化
        function gameUpdate() {
            if (!gamePaused) {
                moveSnake();
            }
            draw();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initGame();
            draw();
            highScoreElement.textContent = highScore;
            displayGameStats();
        });

        // 页面失去焦点时自动暂停
        window.addEventListener('blur', () => {
            if (gameRunning && !gamePaused) {
                pauseGame();
            }
        });

        // 防止页面滚动
        window.addEventListener('keydown', (e) => {
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(e.key)) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
