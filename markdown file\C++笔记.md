  # C++

编译：源代码翻译为机器码，生成二进制文件，一次性交给计算机执行

.cpp文件（源代码文件）—编译——目标代码.obj，调用库文件进行链接生成可执行文件.exe

C++标准

主要是C++98/03  C++11   C++20（新特性）

——————————

std：命名空间

：： 作用域

x64   平台架构

面向过程：一条一条根据问题写

把单独一个功能包装起来需要时调用

——————————————

可以新建项目，把头文件 函数放进去,在另一个包含主函数的项目中使用该函数。在主函数中加入函数声明，例如：void welcome();

——————————————

## 一、语法规则

### 1.变量和数据类型

***标识符***由字母、数字和下划线组成；不能以数字开头；区分大小写

- 变量名一般用小写字母
- 类名一般用大写字母开头
- 包含很多单词用下划线分隔

***作用域***

同一个名字不同的作用域，指代不同的实体

定义在所有花括号外的变量是**全局变量**，在某个花括号内的变量是**局部变量**

相同名字的变量，输出时**以作用域小的为准**；输出时加上: :变为默认全局作用域

——————————————

### 2.常量

（1）符号常量 

#define zero 0将文本中zero 都赋0；**没有数据类型**， **一般用全大写名称**

（2）const限定

​	常量无法修改，必须辅助，**一般首字母大写**

### 基本数据类型
#### (1) **整形**
   - 一个字节有8位bit,一个字节能表示的最大数是2^8=256个数字
   - 包含char short int long longlong,区别就是占据不同的字节
   - 较特殊的是布尔类型bool
#### (2) **无符号整形**
    - 在整形前面加上unsigned
    - 不表示负数,即正数表示范围扩大一倍
   - 一般正数计算全部用Int
   - 超过了范围就用long long
   - 确定不用负值 添加unsigned

#### (3) **较特殊的两种**
  - char默认下不确定有无符号
  - bool常用做真值判断,0假1真,bool的取值是true false,通常占8位
  - bool变量定义时给true /false,打印时对应的是1/0.
#### (4) **浮点类型**
  - float/ double /long double
#### (5) **字面值常量**
  直接写的一个整数,小数
  - 30 十进制
  - 036 八进制
  - 0x1E 十六进制
  默认是int类型,超出范围就选择可表示这个数,长度最小的那个类型

  所以,可以加上后缀:
 -  l或者L,表示long;
 - ll或者LL,表示long long;
 - u或者U,表示无符号类型;
 - f或者F,表示float;
 - l后者L,表示long double(数字是小数时)
  
#### (6) **转义字符**
- \n表示换行
- \t表示横向制表
- \ \本身输出反斜线\
- \\ \? 输出问号
- \\ \'   输出'
- \\ \"   输出"
***
### 3.运算优先级
#### (1) 表达式和运算符
##### 运算优先级和结合律
 
 从左往右,先算乘除,再算加减,小括号优先级最高;
#### (2)算术运算
- 加+ 减- 乘* 除/ 
- 取余%
#### (3)赋值
- 赋值是 = 
- 左侧是左值,右侧是右值,左侧不可以是表达式;
- 已定义的常量不能再赋值;
- C++11中,可以用{}括起来的数值列表作为值赋给左侧;
- 右结合律;    
--
  ***复合运算运算符***
sum = a;
sum = sum + b;
sum = sum + c;
===>
sum += b;
sum +=c;

 ***递增递减运算符***
 ++a 相当于a += 1;
 --b 相当于b -= 1;
 //前置时先递增再赋值,后置还赋值时,先给已有值,自己再加1;
 一般用++i;

#### (4)关系和逻辑运算
##### 关系运算符

- ">" "<"大于小于
- "==" "!="等于不等于
- >关系运算后得到的是Bool类型,参与运算真为1,假为0
- 算数运算符优先级高于关系运算符;
##### 逻辑运算符
把bool类型的关系运算结果组合起来
- 逻辑非(!)
- 逻辑与(&&)
- 逻辑或(||)
逻辑与优先级高于逻辑或,逻辑非优先级最高-

***短路求值***
逻辑运算左侧值已经决定结果,则不计算右侧的运算了,求整体的结果;
