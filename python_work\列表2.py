#修改 添加 删除列表元素
#练习3-4
jiabin = ['<PERSON><PERSON><PERSON>','<PERSON> jun wei','sunbin']
print(f"invite {jiabin[0]} ,{jiabin[1]},{jiabin[2]} to dinner\n")

#练习3-5
print(f"{jiabin[-1]} can't come here")
#修改嘉宾
jiabin[-1] = 'Li zhenda'
print(f"invite {jiabin[0]} ,{jiabin[1]},{jiabin[2]} to dinner\n")

#练习3-6
#添加嘉宾
print("I find a bigger table")
jiabin.insert(0,'Tian xiande')
jiabin.insert(2,'<PERSON> tingfeng')
jiabin.append('Qin jiaze')#append固定在结尾加元素
print(f"invite {jiabin[0]},{jiabin[1]},{jiabin[2]},{jiabin[3]},{jiabin[4]},{jiabin[5]} to dinner\n")

#练习3-7
#缩减嘉宾
print("I just can invite 2 people, becuse the table won't be expressed")
a = jiabin.pop()
print(f"{a},I'm sorry")
b = jiabin.pop()
print(f"{b},I'm sorry")
c = jiabin.pop()
print(f"{c},I'm sorry")
d = jiabin.pop()
print(f"{d},I'm sorry")


print(f"{jiabin[0]},{jiabin[1]} you are still invited")
#删没嘉宾
del jiabin[1]
del jiabin[0]#注意用索引删除后，其余元素的索引有变化
print(jiabin)

