
#print("hello world!")
##语法高亮 print函数一个颜色  字符串非代码另一个颜色

message = "hello world!"#变量与文本 '关联' 起来   
print(message)#打印与变量’关联‘的文本

message = "hello python world!"#python语言对于变量记录新的赋值
print(message)

favorite_language = 'python '
print(favorite_language)
print(favorite_language.rstrip())#暂时性删除空白  并没有删除  要想“永久”删除需要将favorite_language.rstrip赋值给变量  
fav = favorite_language.rstrip()
print(fav)
#使用strip()删除前后空白  
#使用lstrip()删除前面空白  
#使用rstrip()删除后面空白。



