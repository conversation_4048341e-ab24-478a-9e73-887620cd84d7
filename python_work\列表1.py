#练习3-1
#列表名为names,存储几个人名，依次访问每个元素，打印
names = ['Sunbin','pantao',' \tLiu jun wei\n ']
print(names[0])
print(names[1])
print(names[2])

#练习3-2
message = f"{names[0].upper()}, Good Morning"
print(message)
message1 = f"{names[1].title()}, Good Morning"
print(message1)
message2 = f"{names[2].lower().strip()}, Good Morning"
print(message2)

#练习3-3
traffic = ['私家车','火车','飞机']
message = f"我去学校是{traffic[1]}和{traffic[2]}的组合"
print(message)