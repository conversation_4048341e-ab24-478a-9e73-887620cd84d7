{"configurations": [{"name": "C/C++: gcc.exe 生成和调试活动文件", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "C:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:\\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\\mingw64\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: gcc.exe 生成活动文件"}, {"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/vscode/<PERSON><PERSON><PERSON>", "program": "d:/vscode/<PERSON><PERSON><PERSON>/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "C:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}, {"name": "C/C++: g++.exe 生成和调试活动文件", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:\\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\\mingw64\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe 生成活动文件"}], "version": "2.0.0"}